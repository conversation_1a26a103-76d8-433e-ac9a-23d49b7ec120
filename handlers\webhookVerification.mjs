/**
 * WhatsApp Webhook Verification Handler
 * Handles GET requests for webhook verification from WhatsApp
 */

import { config } from '../config/environment.mjs';

/**
 * Handles WhatsApp webhook verification
 * @param {Object} event - Lambda event object
 * @returns {Object} Lambda response object
 */
export async function handleWebhookVerification(event) {
  try {
    console.log('Webhook verification request received:', JSON.stringify(event, null, 2));
     // Extract query parameters
    const queryParams = event.queryStringParameters || {};
     const mode = queryParams['hub.mode'];
    const token = queryParams['hub.verify_token'];
    const challenge = queryParams['hub.challenge'];

 
    // Verify the mode and token
    if (mode === 'subscribe' && token === config.whatsapp.verifyToken) {
      console.log('Webhook verification successful');
      
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'text/plain'
        },
        body: challenge
      };
    } else {
      console.error('Webhook verification failed:', {
        expectedMode: 'subscribe',
        receivedMode: mode,
        tokenMatch: token === config.whatsapp.verifyToken
      });

      return {
        statusCode: 403,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          error: 'Forbidden',
          message: 'Webhook verification failed'
        })
      };
    }
  } catch (error) {
    console.error('Error in webhook verification:', error);
    
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        error: 'Internal Server Error',
        message: 'Failed to process webhook verification'
      })
    };
  }
}
