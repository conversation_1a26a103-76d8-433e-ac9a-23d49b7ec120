/**
 * User Management Service for PostgreSQL
 * Handles user information storage and retrieval
 */

import pkg from 'pg';
const { Pool } = pkg;
import { config } from '../config/environment.mjs';

class UserService {
  constructor() {
    this.pool = new Pool({
      host: config.database.host,
      port: config.database.port,
      database: config.database.database,
      user: config.database.username,
      password: config.database.password,
      ssl: config.database.ssl ? { rejectUnauthorized: false } : false,
      max: 5,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    });
  }

  /**
   * Stores or updates user information
   * @param {string} phoneNumber - User's phone number
   * @param {string} name - User's name (optional)
   * @returns {Object} User information
   */
  async upsertUser(phoneNumber, name = null) {
    try {
      console.log(`Upserting user: ${phoneNumber}, name: ${name}`);

      const client = await this.pool.connect();
      // create table if not exists
      await this.initializeSchema();

      try {
        const upsertQuery = `
          INSERT INTO concierge_users (phone_number, name, created_at, updated_at)
          VALUES ($1, $2, NOW(), NOW())
          ON CONFLICT (phone_number)
          DO UPDATE SET
            name = COALESCE($2, concierge_users.name),
            updated_at = NOW()
          RETURNING id, phone_number, name, created_at, updated_at
        `;

        const result = await client.query(upsertQuery, [phoneNumber, name]);

        const user = result.rows[0];
        console.log(`User upserted successfully:`, user);

        return {
          id: user.id,
          phoneNumber: user.phone_number,
          name: user.name,
          createdAt: user.created_at,
          updatedAt: user.updated_at
        };

      } finally {
        client.release();
      }

    } catch (error) {
      console.error('Error upserting user:', error);
      throw error;
    }
  }

  /**
   * Gets user information by phone number
   * @param {string} phoneNumber - User's phone number
   * @returns {Object|null} User information or null if not found
   */
  async getUserByPhone(phoneNumber) {
    try {
      const client = await this.pool.connect();

      try {
        const selectQuery = `
          SELECT id, phone_number, name, created_at, updated_at
          FROM concierge_users
          WHERE phone_number = $1
        `;

        const result = await client.query(selectQuery, [phoneNumber]);

        if (result.rows.length === 0) {
          return null;
        }

        const user = result.rows[0];
        return {
          id: user.id,
          phoneNumber: user.phone_number,
          name: user.name,
          createdAt: user.created_at,
          updatedAt: user.updated_at
        };

      } finally {
        client.release();
      }

    } catch (error) {
      console.error('Error getting user by phone:', error);
      return null;
    }
  }

  /**
   * Updates user's name
   * @param {string} phoneNumber - User's phone number
   * @param {string} name - User's new name
   * @returns {boolean} Success status
   */
  async updateUserName(phoneNumber, name) {
    try {
      const client = await this.pool.connect();

      try {
        const updateQuery = `
          UPDATE concierge_users
          SET name = $2, updated_at = NOW()
          WHERE phone_number = $1
          RETURNING id
        `;

        const result = await client.query(updateQuery, [phoneNumber, name]);

        if (result.rows.length === 0) {
          console.warn(`User with phone ${phoneNumber} not found for name update`);
          return false;
        }

        console.log(`Updated name for user ${phoneNumber} to ${name}`);
        return true;

      } finally {
        client.release();
      }

    } catch (error) {
      console.error('Error updating user name:', error);
      return false;
    }
  }

  /**
   * Gets all concierge_users (for admin purposes)
   * @param {number} limit - Maximum number of concierge_users to return
   * @param {number} offset - Offset for pagination
   * @returns {Array} Array of concierge_users
   */
  async getAllUsers(limit = 100, offset = 0) {
    try {
      const client = await this.pool.connect();

      try {
        const selectQuery = `
          SELECT id, phone_number, name, created_at, updated_at
          FROM concierge_users
          ORDER BY created_at DESC
          LIMIT $1 OFFSET $2
        `;

        const result = await client.query(selectQuery, [limit, offset]);

        return result.rows.map(user => ({
          id: user.id,
          phoneNumber: user.phone_number,
          name: user.name,
          createdAt: user.created_at,
          updatedAt: user.updated_at
        }));

      } finally {
        client.release();
      }

    } catch (error) {
      console.error('Error getting all concierge_users:', error);
      return [];
    }
  }

  /**
   * Initializes the database schema for user storage
   */
  async initializeSchema() {
    try {
      const client = await this.pool.connect();

      try {
        // Create concierge_users table if not exists
        const createTableQuery = `
          CREATE TABLE IF NOT EXISTS concierge_users (
            id SERIAL PRIMARY KEY,
            phone_number VARCHAR(20) UNIQUE NOT NULL,
            name VARCHAR(255),
            created_at TIMESTAMP DEFAULT NOW(),
            updated_at TIMESTAMP DEFAULT NOW()
          )
        `;

        await client.query(createTableQuery);

        // Create index for phone number lookups
        await client.query(`
          CREATE INDEX IF NOT EXISTS users_phone_number_idx
          ON concierge_users (phone_number)
        `);

        console.log('User database schema initialized successfully');

      } finally {
        client.release();
      }

    } catch (error) {
      console.error('Error initializing user database schema:', error);
      throw error;
    }
  }

  /**
   * Closes the database connection pool
   */
  async close() {
    await this.pool.end();
  }
}

export const userService = new UserService();
