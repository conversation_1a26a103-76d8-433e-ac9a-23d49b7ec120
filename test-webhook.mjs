/**
 * Simple test script for WhatsApp webhook verification
 * Run this after starting the local server with npm start
 */

import axios from 'axios';

const BASE_URL = 'http://localhost:3000';

async function testWebhookVerification() {
  try {
    console.log('🧪 Testing WhatsApp webhook verification...');
    
    const response = await axios.get(`${BASE_URL}/`, {
      params: {
        'hub.mode': 'subscribe',
        'hub.verify_token': 'WeBhoOk',
        'hub.challenge': 'test_challenge_123'
      }
    });
    
    console.log('✅ Webhook verification successful!');
    console.log('Status:', response.status);
    console.log('Response:', response.data);
    
  } catch (error) {
    console.error('❌ Webhook verification failed:', error.response?.data || error.message);
  }
}

async function testInvalidToken() {
  try {
    console.log('\n🧪 Testing webhook verification with invalid token...');
    
    const response = await axios.get(`${BASE_URL}/`, {
      params: {
        'hub.mode': 'subscribe',
        'hub.verify_token': 'invalid_token',
        'hub.challenge': 'test_challenge_123'
      }
    });
    
    console.log('❌ This should have failed!');
    
  } catch (error) {
    if (error.response?.status === 403) {
      console.log('✅ Invalid token correctly rejected!');
      console.log('Status:', error.response.status);
      console.log('Response:', error.response.data);
    } else {
      console.error('❌ Unexpected error:', error.response?.data || error.message);
    }
  }
}

async function testMessageEndpoint() {
  try {
    console.log('\n🧪 Testing message endpoint...');
    
    const messagePayload = {
      entry: [{
        changes: [{
          field: 'messages',
          value: {
            messages: [{
              from: '1234567890',
              type: 'text',
              text: {
                body: 'Hello, I am looking for Italian restaurants'
              }
            }]
          }
        }]
      }]
    };
    
    const response = await axios.post(`${BASE_URL}/`, messagePayload);
    
    console.log('✅ Message endpoint test successful!');
    console.log('Status:', response.status);
    console.log('Response:', response.data);
    
  } catch (error) {
    console.error('❌ Message endpoint test failed:', error.response?.data || error.message);
  }
}

// Run tests
async function runTests() {
  console.log('🚀 Starting WhatsApp webhook tests...');
  console.log('Make sure the server is running with: npm start\n');
  
  await testWebhookVerification();
  await testInvalidToken();
  await testMessageEndpoint();
  
  console.log('\n🎉 Tests completed!');
}

runTests();
