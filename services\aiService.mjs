/**
 * AI Service for processing user messages with AWS Bedrock
 * Handles conversational AI and business data queries
 */

import { BedrockRuntimeClient, InvokeModelCommand } from '@aws-sdk/client-bedrock-runtime';
import { BedrockAgentRuntimeClient, InvokeAgentCommand } from '@aws-sdk/client-bedrock-agent-runtime';
import { config } from '../config/environment.mjs';
import { userService } from './userService.mjs';

class AIService {
  constructor() {
    this.bedrockClient = new BedrockRuntimeClient({ 
      region: config.aws.region 
    });
    
    this.bedrockAgentClient = new BedrockAgentRuntimeClient({ 
      region: config.aws.region 
    });
  }

  /**
   * Processes user message and generates AI response
   * @param {string} userMessage - The user's message
   * @param {string} userPhone - User's phone number for context
   * @returns {string} AI generated response
   */
  async processUserMessage(userMessage, userPhone) {
    try {
      console.log(`Processing message for user ${userPhone}: ${userMessage}`);

      // Get or create user record
      await userService.upsertUser(userPhone);

      // If we have a Bedrock agent configured, use it
      if (config.aws.bedrock.agentId) {
        return await this.invokeBedrockAgent(userMessage, userPhone);
      } else {
        // Fallback to direct model invocation
        return await this.invokeBedrockModel(userMessage);
      }

    } catch (error) {
      console.error('Error in AI service:', error);
      return "I'm sorry, I'm having trouble understanding your request right now. Could you please try rephrasing your question?";
    }
  }

  /**
   * Invokes AWS Bedrock Agent for conversational AI
   * @param {string} userMessage - User's message
   * @param {string} userPhone - User's phone number
   * @returns {string} Agent response
   */
  async invokeBedrockAgent(userMessage, userPhone) {
    try {
      const sessionId = `whatsapp-${userPhone}-${Date.now()}`;
      
      const command = new InvokeAgentCommand({
        agentId: config.aws.bedrock.agentId,
        agentAliasId: config.aws.bedrock.agentAliasId,
        sessionId: sessionId,
        inputText: userMessage
      });

      const response = await this.bedrockAgentClient.send(command);
      
      // Process the streaming response
      let fullResponse = '';
      if (response.completion) {
        for await (const chunk of response.completion) {
          if (chunk.chunk && chunk.chunk.bytes) {
            const text = new TextDecoder().decode(chunk.chunk.bytes);
            fullResponse += text;
          }
        }
      }

      return fullResponse || "I'm sorry, I couldn't generate a response. Please try again.";

    } catch (error) {
      console.error('Error invoking Bedrock Agent:', error);
      throw error;
    }
  }

  /**
   * Invokes AWS Bedrock model directly for AI responses
   * @param {string} userMessage - User's message
   * @returns {string} Model response
   */
  async invokeBedrockModel(userMessage) {
    try {
      const prompt = this.buildPrompt(userMessage);
      
      const command = new InvokeModelCommand({
        modelId: config.aws.bedrock.modelId,
        contentType: 'application/json',
        accept: 'application/json',
        body: JSON.stringify({
          anthropic_version: 'bedrock-2023-05-31',
          max_tokens: 1000,
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ]
        })
      });

      const response = await this.bedrockClient.send(command);
      const responseBody = JSON.parse(new TextDecoder().decode(response.body));
      
      return responseBody.content[0].text || "I'm sorry, I couldn't generate a response.";

    } catch (error) {
      console.error('Error invoking Bedrock model:', error);
      throw error;
    }
  }

  /**
   * Builds the prompt for the AI model
   * @param {string} userMessage - User's message
   * @returns {string} Formatted prompt
   */
  buildPrompt(userMessage) {
    return `You are Cravin Concierge, a helpful AI assistant for local business recommendations and information.
You help concierge_users find restaurants, cafes, and other local businesses based on their preferences and needs.

User's question: ${userMessage}

Please provide a helpful, friendly, and informative response. Use your knowledge base to recommend businesses, include relevant details like location, cuisine type, price range, or special features when available. Keep responses concise but informative.`;
  }
}

export const aiService = new AIService();
